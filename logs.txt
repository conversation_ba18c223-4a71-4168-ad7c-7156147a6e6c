========================================================
    Simple File Transfer SDK Build Script
    Version: 1.0.0
    Time: 2025/06/23 周一 14:32:26.35
========================================================
[INFO] 2025/06/23 周一 14:32:26.35 - Execution mode: build-test
[INFO] 2025/06/23 周一 14:32:26.35 - Server management: enabled
[STEP 1] 2025/06/23 周一 14:32:26.35 - Setting up Java environment
========================================
[INFO] 2025/06/23 周一 14:32:26.35 - Calling Java environment setup script
Java environment variables have been set:
  JAVA_HOME: D:\Data\Green\jdks\zulu8.86.0.25-ca-jdk8.0.452-win_x64
  Java version: openjdk version "1.8.0_452"

Maven options: -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m -Djava.security.policy=all.policy

Environment variable setup completed
[INFO] 2025/06/23 周一 14:32:26.35 - Java environment setup script executed successfully
[SUCCESS] 2025/06/23 周一 14:32:26.63 - Java environment setup completed
[STEP 2] 2025/06/23 周一 14:32:26.63 - Checking Maven environment
========================================
[INFO] 2025/06/23 周一 14:32:26.67 - Maven found in PATH
[SUCCESS] 2025/06/23 周一 14:32:26.67 - Maven environment check completed
[STEP 3] 2025/06/23 周一 14:32:26.67 - Validating project structure
========================================
[INFO] 2025/06/23 周一 14:32:26.67 - Root pom.xml file exists
[SUCCESS] 2025/06/23 周一 14:32:26.67 - Project structure validation completed
[STEP 4] 2025/06/23 周一 14:32:26.67 - Cleaning build environment
========================================
[INFO] 2025/06/23 周一 14:32:26.67 - Cleaning Maven build cache...
[DEBUG] About to execute: call mvn clean
[INFO] Scanning for projects...
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] 文件传输SDK父项目                                                         [pom]
[INFO] 文件传输服务端SDK                                                         [jar]
[INFO] 文件传输客户端SDK                                                         [jar]
[INFO] 文件传输客户端演示应用                                                        [jar]
[INFO] 文件传输独立服务端                                                          [jar]
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-sdk-parent >----------
[INFO] Building 文件传输SDK父项目 1.0.0                                          [1/5]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ file-transfer-sdk-parent ---
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-server-sdk >----------
[INFO] Building 文件传输服务端SDK 1.0.0                                          [2/5]
[INFO]   from file-transfer-server-sdk\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ file-transfer-server-sdk ---
[INFO] Deleting D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-client-sdk >----------
[INFO] Building 文件传输客户端SDK 1.0.0                                          [3/5]
[INFO]   from file-transfer-client-sdk\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ file-transfer-client-sdk ---
[INFO] Deleting D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target
[INFO] 
[INFO] ---------< com.sdesrd.filetransfer:file-transfer-client-demo >----------
[INFO] Building 文件传输客户端演示应用 1.0.0                                         [4/5]
[INFO]   from file-transfer-client-demo\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ file-transfer-client-demo ---
[INFO] Deleting D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target
[INFO] 
[INFO] ------< com.sdesrd.filetransfer:file-transfer-server-standalone >-------
[INFO] Building 文件传输独立服务端 1.0.0                                           [5/5]
[INFO]   from file-transfer-server-standalone\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ file-transfer-server-standalone ---
[INFO] Deleting D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for 文件传输SDK父项目 1.0.0:
[INFO] 
[INFO] 文件传输SDK父项目 ......................................... SUCCESS [  0.196 s]
[INFO] 文件传输服务端SDK ......................................... SUCCESS [  0.069 s]
[INFO] 文件传输客户端SDK ......................................... SUCCESS [  0.054 s]
[INFO] 文件传输客户端演示应用 ........................................ SUCCESS [  0.025 s]
[INFO] 文件传输独立服务端 .......................................... FAILURE [  1.152 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  1.806 s
[INFO] Finished at: 2025-06-23T14:32:29+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-clean-plugin:3.2.0:clean (default-clean) on project file-transfer-server-standalone: Failed to clean project: Failed to delete D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target\file-transfer-server-standalone-1.0.0.jar -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
[ERROR] 
[ERROR] After correcting the problems, you can resume the build with the command
[ERROR]   mvn <args> -rf :file-transfer-server-standalone
[DEBUG] Maven clean completed with errorlevel: 1
[WARNING] 2025/06/23 周一 14:32:29.65 - Maven clean failed, continuing anyway
[SUCCESS] 2025/06/23 周一 14:32:29.65 - Environment cleanup completed
[DEBUG] About to proceed to Step 5
[STEP 5] 2025/06/23 周一 14:32:29.65 - Compiling project
========================================
[INFO] 2025/06/23 周一 14:32:29.65 - Starting compilation of entire project...
[INFO] 2025/06/23 周一 14:32:29.65 - Compile command: mvn compile -T 1C
[INFO] Scanning for projects...
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] 文件传输SDK父项目                                                         [pom]
[INFO] 文件传输服务端SDK                                                         [jar]
[INFO] 文件传输客户端SDK                                                         [jar]
[INFO] 文件传输客户端演示应用                                                        [jar]
[INFO] 文件传输独立服务端                                                          [jar]
[INFO] 
[INFO] Using the MultiThreadedBuilder implementation with a thread count of 16
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-sdk-parent >----------
[INFO] Building 文件传输SDK父项目 1.0.0                                          [1/5]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-client-sdk >----------
[INFO] Building 文件传输客户端SDK 1.0.0                                          [2/5]
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-server-sdk >----------
[INFO]   from file-transfer-client-sdk\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] Building 文件传输服务端SDK 1.0.0                                          [3/5]
[INFO]   from file-transfer-server-sdk\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-client-sdk ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\resources
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ file-transfer-client-sdk ---
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-server-sdk ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 2 resources
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ file-transfer-server-sdk ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 20 source files to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\classes
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 37 source files to D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\classes
[INFO] 
[INFO] ---------< com.sdesrd.filetransfer:file-transfer-client-demo >----------
[INFO] Building 文件传输客户端演示应用 1.0.0                                         [4/5]
[INFO]   from file-transfer-client-demo\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-client-demo ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 1 resource
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ file-transfer-client-demo ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 4 source files to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\classes
[INFO] 
[INFO] ------< com.sdesrd.filetransfer:file-transfer-server-standalone >-------
[INFO] Building 文件传输独立服务端 1.0.0                                           [5/5]
[INFO]   from file-transfer-server-standalone\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-server-standalone ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 0 resource
[INFO] Copying 1 resource
[INFO] 
[INFO] --- compiler:3.10.1:compile (default-compile) @ file-transfer-server-standalone ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 1 source file to D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target\classes
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for 文件传输SDK父项目 1.0.0:
[INFO] 
[INFO] 文件传输SDK父项目 ......................................... SUCCESS [  0.059 s]
[INFO] 文件传输服务端SDK ......................................... SUCCESS [  5.438 s]
[INFO] 文件传输客户端SDK ......................................... SUCCESS [  4.287 s]
[INFO] 文件传输客户端演示应用 ........................................ SUCCESS [  2.444 s]
[INFO] 文件传输独立服务端 .......................................... SUCCESS [  0.792 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  7.063 s (Wall Clock)
[INFO] Finished at: 2025-06-23T14:32:37+08:00
[INFO] ------------------------------------------------------------------------
[SUCCESS] 2025/06/23 周一 14:32:38.00 - Project compilation successful
[STEP 6] 2025/06/23 周一 14:32:38.00 - Installing project to local Maven repository
========================================
[INFO] 2025/06/23 周一 14:32:38.00 - Starting installation of project to local Maven repository...
[INFO] 2025/06/23 周一 14:32:38.00 - Install command: mvn install -DskipTests -T 1C
[INFO] Scanning for projects...
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] 文件传输SDK父项目                                                         [pom]
[INFO] 文件传输服务端SDK                                                         [jar]
[INFO] 文件传输客户端SDK                                                         [jar]
[INFO] 文件传输客户端演示应用                                                        [jar]
[INFO] 文件传输独立服务端                                                          [jar]
[INFO] 
[INFO] Using the MultiThreadedBuilder implementation with a thread count of 16
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-sdk-parent >----------
[INFO] Building 文件传输SDK父项目 1.0.0                                          [1/5]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] >>> source:3.2.1:jar (attach-sources) > generate-sources @ file-transfer-sdk-parent >>>
[INFO] 
[INFO] <<< source:3.2.1:jar (attach-sources) < generate-sources @ file-transfer-sdk-parent <<<
[INFO] 
[INFO] 
[INFO] --- source:3.2.1:jar (attach-sources) @ file-transfer-sdk-parent ---
[INFO] 
[INFO] --- javadoc:3.2.0:jar (attach-javadocs) @ file-transfer-sdk-parent ---
[INFO] Not executing Javadoc as the project is not a Java classpath-capable package
[INFO] 
[INFO] --- install:3.1.2:install (default-install) @ file-transfer-sdk-parent ---
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\pom.xml to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-sdk-parent\1.0.0\file-transfer-sdk-parent-1.0.0.pom
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-server-sdk >----------
[INFO] Building 文件传输服务端SDK 1.0.0                                          [2/5]
[INFO]   from file-transfer-server-sdk\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] ----------< com.sdesrd.filetransfer:file-transfer-client-sdk >----------
[INFO] Building 文件传输客户端SDK 1.0.0                                          [3/5]
[INFO]   from file-transfer-client-sdk\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-client-sdk ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\resources
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ file-transfer-client-sdk ---
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-server-sdk ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 2 resources
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ file-transfer-server-sdk ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.2.0:testResources (default-testResources) @ file-transfer-client-sdk ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] --- resources:3.2.0:testResources (default-testResources) @ file-transfer-server-sdk ---
[INFO] skip non existing resourceDirectory D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\test\resources
[INFO] 
[INFO] --- compiler:3.8.1:testCompile (default-testCompile) @ file-transfer-client-sdk ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\test\resources
[INFO] 
[INFO] --- compiler:3.8.1:testCompile (default-testCompile) @ file-transfer-server-sdk ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 6 source files to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\test-classes
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 15 source files to D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\test-classes
[INFO] 
[INFO] --- surefire:3.0.0-M7:test (default-test) @ file-transfer-client-sdk ---
[INFO] Tests are skipped.
[INFO] 
[INFO] --- jar:3.4.1:jar (default-jar) @ file-transfer-client-sdk ---
[INFO] 
[INFO] --- surefire:3.0.0-M7:test (default-test) @ file-transfer-server-sdk ---
[INFO] Tests are skipped.
[INFO] 
[INFO] --- jar:3.4.1:jar (default-jar) @ file-transfer-server-sdk ---
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\file-transfer-client-sdk-1.0.0.jar
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\file-transfer-server-sdk-1.0.0.jar
[INFO] 
[INFO] 
[INFO] >>> source:3.2.1:jar (attach-sources) > generate-sources @ file-transfer-client-sdk >>>
[INFO] >>> source:3.2.1:jar (attach-sources) > generate-sources @ file-transfer-server-sdk >>>
[INFO] 
[INFO] 
[INFO] <<< source:3.2.1:jar (attach-sources) < generate-sources @ file-transfer-client-sdk <<<
[INFO] <<< source:3.2.1:jar (attach-sources) < generate-sources @ file-transfer-server-sdk <<<
[INFO] 
[INFO] 
[INFO] 
[INFO] 
[INFO] --- source:3.2.1:jar (attach-sources) @ file-transfer-client-sdk ---
[INFO] --- source:3.2.1:jar (attach-sources) @ file-transfer-server-sdk ---
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\file-transfer-client-sdk-1.0.0-sources.jar
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\file-transfer-server-sdk-1.0.0-sources.jar
[INFO] 
[INFO] 
[INFO] --- javadoc:3.2.0:jar (attach-javadocs) @ file-transfer-server-sdk ---
[INFO] --- javadoc:3.2.0:jar (attach-javadocs) @ file-transfer-client-sdk ---
[INFO] No previous run data found, generating javadoc.
[INFO] No previous run data found, generating javadoc.
[INFO] 
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\config\ClientAuthConfig.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\config\ClientConfig.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\config\ClientConfigBuilder.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\ApiResult.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\DownloadResult.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\FileInfo.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\FileUploadCompleteResponse.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\FileUploadInitRequest.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\FileUploadInitResponse.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\TransferProgress.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\dto\UploadResult.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\exception\FileTransferException.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\FileTransferClient.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\listener\TransferListener.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\util\AuthUtils.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\util\ConcurrentTransferManager.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\util\DownloadManager.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\util\FileUtils.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\util\RetryManager.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\src\main\java\com\sdesrd\filetransfer\client\util\UlidValidator.java...
正在构造 Javadoc 信息...
标准 Doclet 版本 1.8.0_452
正在构建所有程序包和类的树...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\exception\FileTransferException.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\FileTransferClient.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\listener\TransferListener.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\AuthUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\ConcurrentTransferManager.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\ConcurrentTransferManager.TransferCallable.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\ConcurrentTransferManager.TransferStats.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\ConcurrentTransferManager.TransferTask.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\ConcurrentTransferManager.TransferType.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\DownloadManager.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\FileUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\FileUtils.ProgressCallback.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\RetryManager.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\RetryManager.RetryPolicy.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\RetryManager.RetryPolicyBuilder.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\UlidValidator.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\ClientAuthConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\ClientConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\ClientConfigBuilder.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\ApiResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\DownloadResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\FileInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\FileUploadCompleteResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\FileUploadInitRequest.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\FileUploadInitResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\TransferProgress.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\UploadResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\overview-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\exception\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\exception\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\exception\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\listener\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\listener\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\listener\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\constant-values.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\serialized-form.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\class-use\ClientAuthConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\class-use\ClientConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\class-use\ClientConfigBuilder.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\ApiResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\DownloadResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\FileInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\FileUploadCompleteResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\FileUploadInitRequest.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\FileUploadInitResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\TransferProgress.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\class-use\UploadResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\exception\class-use\FileTransferException.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\class-use\FileTransferClient.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\listener\class-use\TransferListener.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\AuthUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\ConcurrentTransferManager.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\ConcurrentTransferManager.TransferType.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\ConcurrentTransferManager.TransferCallable.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\ConcurrentTransferManager.TransferTask.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\ConcurrentTransferManager.TransferStats.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\DownloadManager.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\FileUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\FileUtils.ProgressCallback.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\RetryManager.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\RetryManager.RetryPolicy.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\RetryManager.RetryPolicyBuilder.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\class-use\UlidValidator.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\config\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\dto\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\exception\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\listener\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\com\sdesrd\filetransfer\client\util\package-use.html...
正在构建所有程序包和类的索引...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\overview-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\index-all.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\deprecated-list.html...
正在构建所有类的索引...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\allclasses-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\allclasses-noframe.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\index.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\overview-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\apidocs\help-doc.html...
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\file-transfer-client-sdk-1.0.0-javadoc.jar
[INFO] 
[INFO] --- install:3.1.2:install (default-install) @ file-transfer-client-sdk ---
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\pom.xml to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-sdk\1.0.0\file-transfer-client-sdk-1.0.0.pom
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\file-transfer-client-sdk-1.0.0.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-sdk\1.0.0\file-transfer-client-sdk-1.0.0.jar
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\file-transfer-client-sdk-1.0.0-sources.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-sdk\1.0.0\file-transfer-client-sdk-1.0.0-sources.jar
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-sdk\target\file-transfer-client-sdk-1.0.0-javadoc.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-sdk\1.0.0\file-transfer-client-sdk-1.0.0-javadoc.jar
[INFO] 
[INFO] ---------< com.sdesrd.filetransfer:file-transfer-client-demo >----------
[INFO] Building 文件传输客户端演示应用 1.0.0                                         [4/5]
[INFO]   from file-transfer-client-demo\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-client-demo ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 1 resource
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ file-transfer-client-demo ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.2.0:testResources (default-testResources) @ file-transfer-client-demo ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\src\test\resources
[INFO] 
[INFO] --- compiler:3.8.1:testCompile (default-testCompile) @ file-transfer-client-demo ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 3 source files to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\test-classes
[INFO] 
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\AsyncConfig.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\DatabaseInitializer.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\DatabaseOptimizationConfig.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\FileTransferAutoConfiguration.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\FileTransferProperties.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\UserConfig.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\config\WebConfig.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\controller\DatabaseManagementController.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\controller\FileTransferAdminController.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\controller\FileTransferController.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\ApiResult.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\FileInfo.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\FileMetadata.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\FileUploadCompleteRequest.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\FileUploadCompleteResponse.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\FileUploadInitRequest.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\FileUploadInitResponse.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\SystemHealthResponse.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\dto\TransferProgressResponse.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\entity\FileChunkRecord.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\entity\FileTransferRecord.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\exception\FileIntegrityException.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\exception\FileTransferException.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\interceptor\AuthInterceptor.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\mapper\FileChunkRecordMapper.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\mapper\FileTransferRecordMapper.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\service\AuthService.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\service\DatabaseFallbackService.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\service\DatabaseManagementService.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\service\FileMetadataService.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\service\FileTransferMonitorService.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\service\FileTransferService.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\util\FileTransferTestUtils.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\util\FileUtils.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\util\PerformanceMonitor.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\util\RateLimitUtils.java...
正在加载源文件D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\src\main\java\com\sdesrd\filetransfer\server\util\UlidUtils.java...
正在构造 Javadoc 信息...
标准 Doclet 版本 1.8.0_452
正在构建所有程序包和类的树...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\ApiResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\FileInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\FileMetadata.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\FileUploadCompleteRequest.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\FileUploadCompleteResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\FileUploadInitRequest.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\FileUploadInitResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\SystemHealthResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\TransferProgressResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\AsyncConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\DatabaseInitializer.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\DatabaseOptimizationConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\DatabaseOptimizationConfig.DatabaseOptimizer.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\FileTransferAutoConfiguration.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\FileTransferProperties.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\UserConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\UserConfig.Role.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\WebConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\FileChunkRecord.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\FileTransferRecord.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\FileTransferTestUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\FileUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\PerformanceMonitor.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\PerformanceMonitor.PerformanceMetrics.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\PerformanceMonitor.TransferStatistics.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\RateLimitUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\UlidUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\FileIntegrityException.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\FileTransferException.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\interceptor\AuthInterceptor.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\AuthService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\DatabaseFallbackService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\DatabaseManagementService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\DatabaseManagementService.BackupFileInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\DatabaseManagementService.DatabaseHealthInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\DatabaseManagementService.DatabaseRebuildResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\FileMetadataService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\FileTransferMonitorService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\FileTransferMonitorService.TransferStatistics.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\FileTransferService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\DatabaseManagementController.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\FileTransferAdminController.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\FileTransferController.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\FileChunkRecordMapper.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\FileTransferRecordMapper.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\overview-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\interceptor\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\interceptor\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\interceptor\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\package-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\package-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\package-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\constant-values.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\serialized-form.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\AsyncConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\DatabaseInitializer.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\DatabaseOptimizationConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\DatabaseOptimizationConfig.DatabaseOptimizer.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\FileTransferAutoConfiguration.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\FileTransferProperties.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\UserConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\UserConfig.Role.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\class-use\WebConfig.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\class-use\DatabaseManagementController.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\class-use\FileTransferAdminController.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\class-use\FileTransferController.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\ApiResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\FileInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\FileMetadata.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\FileUploadCompleteRequest.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\FileUploadCompleteResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\FileUploadInitRequest.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\FileUploadInitResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\SystemHealthResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\class-use\TransferProgressResponse.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\class-use\FileChunkRecord.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\class-use\FileTransferRecord.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\class-use\FileIntegrityException.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\class-use\FileTransferException.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\interceptor\class-use\AuthInterceptor.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\class-use\FileChunkRecordMapper.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\class-use\FileTransferRecordMapper.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\AuthService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\DatabaseFallbackService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\DatabaseManagementService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\DatabaseManagementService.DatabaseHealthInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\DatabaseManagementService.BackupFileInfo.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\DatabaseManagementService.DatabaseRebuildResult.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\FileMetadataService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\FileTransferMonitorService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\FileTransferMonitorService.TransferStatistics.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\class-use\FileTransferService.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\FileTransferTestUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\FileUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\PerformanceMonitor.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\PerformanceMonitor.PerformanceMetrics.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\PerformanceMonitor.TransferStatistics.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\RateLimitUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\class-use\UlidUtils.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\config\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\controller\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\dto\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\entity\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\exception\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\interceptor\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\mapper\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\service\package-use.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\com\sdesrd\filetransfer\server\util\package-use.html...
正在构建所有程序包和类的索引...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\overview-tree.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\index-all.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\deprecated-list.html...
正在构建所有类的索引...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\allclasses-frame.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\allclasses-noframe.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\index.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\overview-summary.html...
正在生成D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\apidocs\help-doc.html...
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\file-transfer-server-sdk-1.0.0-javadoc.jar
[INFO] 
[INFO] --- install:3.1.2:install (default-install) @ file-transfer-server-sdk ---
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\pom.xml to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-server-sdk\1.0.0\file-transfer-server-sdk-1.0.0.pom
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\file-transfer-server-sdk-1.0.0.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-server-sdk\1.0.0\file-transfer-server-sdk-1.0.0.jar
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\file-transfer-server-sdk-1.0.0-sources.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-server-sdk\1.0.0\file-transfer-server-sdk-1.0.0-sources.jar
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-server-sdk\target\file-transfer-server-sdk-1.0.0-javadoc.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-server-sdk\1.0.0\file-transfer-server-sdk-1.0.0-javadoc.jar
[INFO] 
[INFO] ------< com.sdesrd.filetransfer:file-transfer-server-standalone >-------
[INFO] Building 文件传输独立服务端 1.0.0                                           [5/5]
[INFO]   from file-transfer-server-standalone\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ file-transfer-server-standalone ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 0 resource
[INFO] Copying 1 resource
[INFO] 
[INFO] --- compiler:3.10.1:compile (default-compile) @ file-transfer-server-standalone ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 1 source file to D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target\classes
[INFO] 
[INFO] --- resources:3.2.0:testResources (default-testResources) @ file-transfer-server-standalone ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\src\test\resources
[INFO] 
[INFO] --- compiler:3.10.1:testCompile (default-testCompile) @ file-transfer-server-standalone ---
[INFO] No sources to compile
[INFO] 
[INFO] --- surefire:2.22.2:test (default-test) @ file-transfer-server-standalone ---
[INFO] Tests are skipped.
[INFO] 
[INFO] --- jar:3.2.2:jar (default-jar) @ file-transfer-server-standalone ---
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target\file-transfer-server-standalone-1.0.0.jar
[INFO] 
[INFO] --- spring-boot:2.7.0:repackage (repackage) @ file-transfer-server-standalone ---
[INFO] 
[INFO] --- surefire:3.2.5:test (default-test) @ file-transfer-client-demo ---
[INFO] Tests are skipped.
[INFO] 
[INFO] --- jar:3.2.2:jar (default-jar) @ file-transfer-client-demo ---
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\file-transfer-client-demo-1.0.0.jar
[INFO] 
[INFO] --- dependency:3.2.0:copy-dependencies (copy-dependencies) @ file-transfer-client-demo ---
[INFO] Copying file-transfer-client-sdk-1.0.0.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\file-transfer-client-sdk-1.0.0.jar
[INFO] Copying okhttp-4.9.3.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\okhttp-4.9.3.jar
[INFO] Copying okio-2.8.0.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\okio-2.8.0.jar
[INFO] Copying kotlin-stdlib-common-1.6.10.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\kotlin-stdlib-common-1.6.10.jar
[INFO] Copying kotlin-stdlib-1.6.10.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\kotlin-stdlib-1.6.10.jar
[INFO] Copying annotations-13.0.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\annotations-13.0.jar
[INFO] Copying fastjson-1.2.78.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\fastjson-1.2.78.jar
[INFO] Copying hutool-crypto-5.3.8.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\hutool-crypto-5.3.8.jar
[INFO] Copying commons-io-2.11.0.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\commons-io-2.11.0.jar
[INFO] Copying commons-lang3-3.12.0.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\commons-lang3-3.12.0.jar
[INFO] Copying hutool-core-5.3.8.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\hutool-core-5.3.8.jar
[INFO] Copying slf4j-api-1.7.36.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\slf4j-api-1.7.36.jar
[INFO] Copying logback-classic-1.2.12.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\logback-classic-1.2.12.jar
[INFO] Copying logback-core-1.2.12.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\logback-core-1.2.12.jar
[INFO] Copying lombok-1.18.38.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\lombok-1.18.38.jar
[INFO] Copying junit-4.13.2.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\junit-4.13.2.jar
[INFO] Copying hamcrest-core-2.2.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\hamcrest-core-2.2.jar
[INFO] Copying hamcrest-2.2.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\hamcrest-2.2.jar
[INFO] Copying mockito-core-4.0.0.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\mockito-core-4.0.0.jar
[INFO] Copying byte-buddy-1.11.22.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\byte-buddy-1.11.22.jar
[INFO] Copying byte-buddy-agent-1.11.22.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\byte-buddy-agent-1.11.22.jar
[INFO] Copying objenesis-3.2.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\objenesis-3.2.jar
[INFO] Copying spring-test-5.3.18.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\spring-test-5.3.18.jar
[INFO] Copying spring-core-5.3.18.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\spring-core-5.3.18.jar
[INFO] Copying spring-jcl-5.3.18.jar to D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\lib\spring-jcl-5.3.18.jar
[INFO] 
[INFO] >>> source:3.2.1:jar (attach-sources) > generate-sources @ file-transfer-client-demo >>>
[INFO] 
[INFO] <<< source:3.2.1:jar (attach-sources) < generate-sources @ file-transfer-client-demo <<<
[INFO] 
[INFO] 
[INFO] --- source:3.2.1:jar (attach-sources) @ file-transfer-client-demo ---
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\file-transfer-client-demo-1.0.0-sources.jar
[INFO] 
[INFO] --- javadoc:3.2.0:jar (attach-javadocs) @ file-transfer-client-demo ---
[INFO] No previous run data found, generating javadoc.
[INFO] Building jar: D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\file-transfer-client-demo-1.0.0-javadoc.jar
[INFO] 
[INFO] --- install:3.1.2:install (default-install) @ file-transfer-client-demo ---
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\pom.xml to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-demo\1.0.0\file-transfer-client-demo-1.0.0.pom
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\file-transfer-client-demo-1.0.0.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-demo\1.0.0\file-transfer-client-demo-1.0.0.jar
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\file-transfer-client-demo-1.0.0-sources.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-demo\1.0.0\file-transfer-client-demo-1.0.0-sources.jar
[INFO] Installing D:\Data\gitdb\file-transfer-sdk\file-transfer-client-demo\target\file-transfer-client-demo-1.0.0-javadoc.jar to C:\Users\<USER>\.m2\repository\com\sdesrd\filetransfer\file-transfer-client-demo\1.0.0\file-transfer-client-demo-1.0.0-javadoc.jar
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for 文件传输SDK父项目 1.0.0:
[INFO] 
[INFO] 文件传输SDK父项目 ......................................... SUCCESS [  1.083 s]
[INFO] 文件传输服务端SDK ......................................... SUCCESS [  9.296 s]
[INFO] 文件传输客户端SDK ......................................... SUCCESS [  8.649 s]
[INFO] 文件传输客户端演示应用 ........................................ SUCCESS [  5.161 s]
[INFO] 文件传输独立服务端 .......................................... FAILURE [  1.738 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  15.161 s (Wall Clock)
[INFO] Finished at: 2025-06-23T14:32:54+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:2.7.0:repackage (repackage) on project file-transfer-server-standalone: Execution repackage of goal org.springframework.boot:spring-boot-maven-plugin:2.7.0:repackage failed: Unable to rename 'D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target\file-transfer-server-standalone-1.0.0.jar' to 'D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone\target\file-transfer-server-standalone-1.0.0.jar.original' -> [Help 1]
[ERROR] java.lang.NullPointerException
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/PluginExecutionException
[ERROR] 
[ERROR] After correcting the problems, you can resume the build with the command
[ERROR]   mvn <args> -rf :file-transfer-server-standalone
[ERROR] 2025/06/23 周一 14:32:54.49 - Project installation failed
